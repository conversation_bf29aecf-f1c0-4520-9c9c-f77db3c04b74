{% extends "base.html" %}

{% block title %}智能凭证创建{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/yonyou-theme.css') }}">
<style>
.wizard-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

.wizard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.wizard-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: rgba(255,255,255,0.3);
}

.step.active::after {
    background: rgba(255,255,255,0.8);
}

.step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    font-weight: 600;
    font-size: 14px;
}

.step.active .step-circle {
    background: white;
    color: #667eea;
}

.step.completed .step-circle {
    background: #27ae60;
    color: white;
}

.step-label {
    font-size: 12px;
    opacity: 0.8;
}

.step.active .step-label {
    opacity: 1;
    font-weight: 600;
}

.wizard-content {
    padding: 30px;
}

.business-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.business-type-card {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.business-type-card:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.business-type-card.selected {
    border-color: #3498db;
    background: #e3f2fd;
}

.business-type-card i {
    font-size: 32px;
    color: #3498db;
    margin-bottom: 10px;
}

.business-type-card h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.form-section {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-section h6 {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.form-section h6 i {
    margin-right: 8px;
    color: #3498db;
}

.voucher-preview {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 20px;
}

.preview-header {
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
}

.preview-table th,
.preview-table td {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    text-align: left;
    font-size: 13px;
}

.preview-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.amount-input {
    text-align: right;
}

.btn-wizard {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    margin: 0 5px;
}

.btn-primary-wizard {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: white;
}

.btn-secondary-wizard {
    background: #95a5a6;
    border: none;
    color: white;
}

.validation-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.validation-message.error {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.validation-message.success {
    background: #d4edda;
    border-color: #c3e6cb;
}

@media (max-width: 768px) {
    .business-type-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wizard-content {
        padding: 20px;
    }
    
    .wizard-steps {
        flex-direction: column;
        gap: 10px;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="wizard-container">
                <!-- 向导头部 -->
                <div class="wizard-header">
                    <h3><i class="fas fa-magic"></i> 智能凭证创建向导</h3>
                    <p>通过简单的步骤，快速创建专业的财务凭证</p>
                    
                    <!-- 步骤指示器 -->
                    <div class="wizard-steps">
                        <div class="step active" id="step1">
                            <div class="step-circle">1</div>
                            <div class="step-label">选择业务类型</div>
                        </div>
                        <div class="step" id="step2">
                            <div class="step-circle">2</div>
                            <div class="step-label">填写基本信息</div>
                        </div>
                        <div class="step" id="step3">
                            <div class="step-circle">3</div>
                            <div class="step-label">预览确认</div>
                        </div>
                        <div class="step" id="step4">
                            <div class="step-circle">4</div>
                            <div class="step-label">完成创建</div>
                        </div>
                    </div>
                </div>

                <!-- 向导内容 -->
                <div class="wizard-content">
                    <!-- 步骤1: 选择业务类型 -->
                    <div class="wizard-step" id="stepContent1">
                        <h5 class="mb-4">请选择业务类型</h5>
                        <div class="business-type-grid">
                            <div class="business-type-card" data-type="purchase">
                                <i class="fas fa-shopping-cart"></i>
                                <h5>采购业务</h5>
                                <p class="text-muted mt-2">原材料采购、商品采购等</p>
                            </div>
                            <div class="business-type-card" data-type="payment">
                                <i class="fas fa-credit-card"></i>
                                <h5>付款业务</h5>
                                <p class="text-muted mt-2">支付应付账款、费用支出等</p>
                            </div>
                            <div class="business-type-card" data-type="sale">
                                <i class="fas fa-chart-line"></i>
                                <h5>销售业务</h5>
                                <p class="text-muted mt-2">商品销售、服务收入等</p>
                            </div>
                            <div class="business-type-card" data-type="receipt">
                                <i class="fas fa-money-bill-wave"></i>
                                <h5>收款业务</h5>
                                <p class="text-muted mt-2">收取应收账款、其他收入等</p>
                            </div>
                            <div class="business-type-card" data-type="expense">
                                <i class="fas fa-file-invoice-dollar"></i>
                                <h5>费用业务</h5>
                                <p class="text-muted mt-2">管理费用、销售费用等</p>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2: 填写基本信息 -->
                    <div class="wizard-step" id="stepContent2" style="display: none;">
                        <h5 class="mb-4">填写业务信息</h5>
                        
                        <div class="form-section">
                            <h6><i class="fas fa-info-circle"></i> 基本信息</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">业务日期</label>
                                    <input type="date" class="form-control" id="businessDate" 
                                           value="{{ date.today().strftime('%Y-%m-%d') }}">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">金额</label>
                                    <input type="number" class="form-control amount-input" id="businessAmount" 
                                           placeholder="0.00" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label class="form-label">业务描述</label>
                                    <input type="text" class="form-control" id="businessDescription" 
                                           placeholder="请输入业务描述">
                                </div>
                            </div>
                        </div>

                        <div id="templatePreview" class="form-section" style="display: none;">
                            <h6><i class="fas fa-eye"></i> 推荐模板</h6>
                            <div id="templateContent"></div>
                        </div>
                    </div>

                    <!-- 步骤3: 预览确认 -->
                    <div class="wizard-step" id="stepContent3" style="display: none;">
                        <h5 class="mb-4">凭证预览</h5>
                        
                        <div class="voucher-preview">
                            <div class="preview-header">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>凭证类型：</strong><span id="previewVoucherType"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>凭证日期：</strong><span id="previewVoucherDate"></span>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>摘要：</strong><span id="previewSummary"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <table class="preview-table">
                                <thead>
                                    <tr>
                                        <th width="15%">科目编码</th>
                                        <th width="25%">科目名称</th>
                                        <th width="30%">摘要</th>
                                        <th width="15%">借方金额</th>
                                        <th width="15%">贷方金额</th>
                                    </tr>
                                </thead>
                                <tbody id="previewDetails">
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3">合计</th>
                                        <th id="totalDebit">0.00</th>
                                        <th id="totalCredit">0.00</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div id="validationResults" class="mt-3"></div>
                    </div>

                    <!-- 步骤4: 完成创建 -->
                    <div class="wizard-step" id="stepContent4" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-check-circle text-success" style="font-size: 64px;"></i>
                            <h4 class="mt-3">凭证创建成功！</h4>
                            <p class="text-muted">凭证号：<span id="createdVoucherNumber"></span></p>
                            
                            <div class="mt-4">
                                <button class="btn btn-primary-wizard" onclick="viewVoucher()">
                                    <i class="fas fa-eye"></i> 查看凭证
                                </button>
                                <button class="btn btn-secondary-wizard" onclick="createAnother()">
                                    <i class="fas fa-plus"></i> 再创建一个
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between mt-4">
                        <button class="btn btn-secondary-wizard" id="prevBtn" onclick="previousStep()" style="display: none;">
                            <i class="fas fa-arrow-left"></i> 上一步
                        </button>
                        <div></div>
                        <button class="btn btn-primary-wizard" id="nextBtn" onclick="nextStep()" disabled>
                            下一步 <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentStep = 1;
let selectedBusinessType = '';
let voucherTemplate = null;
let createdVoucherId = null;

$(document).ready(function() {
    // 业务类型选择
    $('.business-type-card').click(function() {
        $('.business-type-card').removeClass('selected');
        $(this).addClass('selected');
        selectedBusinessType = $(this).data('type');
        $('#nextBtn').prop('disabled', false);
    });

    // 金额输入监听
    $('#businessAmount').on('input', function() {
        const amount = parseFloat($(this).val()) || 0;
        if (amount > 0 && selectedBusinessType) {
            loadTemplate();
        }
    });

    // 描述输入监听
    $('#businessDescription').on('input', function() {
        updatePreview();
    });
});

// 下一步
function nextStep() {
    if (currentStep < 4) {
        if (validateCurrentStep()) {
            currentStep++;
            updateStepDisplay();
            
            if (currentStep === 3) {
                generatePreview();
            } else if (currentStep === 4) {
                createVoucher();
            }
        }
    }
}

// 上一步
function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

// 更新步骤显示
function updateStepDisplay() {
    // 更新步骤指示器
    $('.step').removeClass('active completed');
    for (let i = 1; i < currentStep; i++) {
        $(`#step${i}`).addClass('completed');
    }
    $(`#step${currentStep}`).addClass('active');

    // 更新内容显示
    $('.wizard-step').hide();
    $(`#stepContent${currentStep}`).show();

    // 更新按钮状态
    $('#prevBtn').toggle(currentStep > 1);
    $('#nextBtn').toggle(currentStep < 4);
    
    if (currentStep === 3) {
        $('#nextBtn').text('创建凭证').prepend('<i class="fas fa-save"></i> ');
    } else {
        $('#nextBtn').html('下一步 <i class="fas fa-arrow-right"></i>');
    }
}

// 验证当前步骤
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            return selectedBusinessType !== '';
        case 2:
            const amount = parseFloat($('#businessAmount').val()) || 0;
            return amount > 0;
        case 3:
            return voucherTemplate !== null;
        default:
            return true;
    }
}

// 加载模板
function loadTemplate() {
    const amount = parseFloat($('#businessAmount').val()) || 0;
    
    if (!selectedBusinessType || amount <= 0) return;

    $.post('/financial/assistant/suggest-template', {
        business_type: selectedBusinessType,
        amount: amount
    })
    .done(function(response) {
        if (response.success) {
            voucherTemplate = response.data;
            displayTemplate();
        }
    })
    .fail(function() {
        showMessage('加载模板失败', 'error');
    });
}

// 显示模板
function displayTemplate() {
    if (!voucherTemplate) return;

    let html = `
        <div class="alert alert-info">
            <strong>推荐模板：</strong>${voucherTemplate.voucher_type}<br>
            <strong>默认摘要：</strong>${voucherTemplate.summary}
        </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>科目编码</th>
                    <th>科目名称</th>
                    <th>借方金额</th>
                    <th>贷方金额</th>
                </tr>
            </thead>
            <tbody>
    `;

    voucherTemplate.details.forEach(function(detail) {
        html += `
            <tr>
                <td>${detail.subject_code}</td>
                <td>${detail.subject_name}</td>
                <td class="text-end">${detail.debit_amount > 0 ? formatCurrency(detail.debit_amount) : ''}</td>
                <td class="text-end">${detail.credit_amount > 0 ? formatCurrency(detail.credit_amount) : ''}</td>
            </tr>
        `;
    });

    html += '</tbody></table>';
    
    $('#templateContent').html(html);
    $('#templatePreview').show();
}

// 生成预览
function generatePreview() {
    if (!voucherTemplate) return;

    const description = $('#businessDescription').val() || voucherTemplate.summary;
    
    $('#previewVoucherType').text(voucherTemplate.voucher_type);
    $('#previewVoucherDate').text($('#businessDate').val());
    $('#previewSummary').text(description);

    let detailsHtml = '';
    let totalDebit = 0;
    let totalCredit = 0;

    voucherTemplate.details.forEach(function(detail) {
        if (detail.subject_id) {
            detailsHtml += `
                <tr>
                    <td>${detail.subject_code}</td>
                    <td>${detail.subject_name}</td>
                    <td>${description}</td>
                    <td class="text-end">${detail.debit_amount > 0 ? formatCurrency(detail.debit_amount) : ''}</td>
                    <td class="text-end">${detail.credit_amount > 0 ? formatCurrency(detail.credit_amount) : ''}</td>
                </tr>
            `;
            totalDebit += detail.debit_amount;
            totalCredit += detail.credit_amount;
        }
    });

    $('#previewDetails').html(detailsHtml);
    $('#totalDebit').text(formatCurrency(totalDebit));
    $('#totalCredit').text(formatCurrency(totalCredit));

    // 验证凭证
    validateVoucher();
}

// 验证凭证
function validateVoucher() {
    const voucherData = {
        voucher_date: $('#businessDate').val(),
        voucher_type: voucherTemplate.voucher_type,
        summary: $('#businessDescription').val() || voucherTemplate.summary
    };

    const detailsData = voucherTemplate.details.filter(d => d.subject_id).map((detail, index) => ({
        line_number: index + 1,
        subject_id: detail.subject_id,
        summary: voucherData.summary,
        debit_amount: detail.debit_amount,
        credit_amount: detail.credit_amount
    }));

    $.post('/financial/assistant/validate', {
        voucher_data: voucherData,
        details_data: detailsData
    })
    .done(function(response) {
        if (response.success) {
            displayValidationResults(response.data);
        }
    });
}

// 显示验证结果
function displayValidationResults(data) {
    let html = '';
    
    if (data.is_valid) {
        html = '<div class="validation-message success"><i class="fas fa-check-circle"></i> 凭证验证通过，可以创建</div>';
    } else {
        html = '<div class="validation-message error"><i class="fas fa-exclamation-triangle"></i> 发现问题：<ul>';
        data.suggestions.forEach(function(suggestion) {
            if (suggestion.type === 'error') {
                html += `<li>${suggestion.message}</li>`;
            }
        });
        html += '</ul></div>';
    }

    if (data.warnings && data.warnings.length > 0) {
        html += '<div class="validation-message"><i class="fas fa-info-circle"></i> 提醒：<ul>';
        data.warnings.forEach(function(warning) {
            html += `<li>${warning.message}</li>`;
        });
        html += '</ul></div>';
    }

    $('#validationResults').html(html);
}

// 创建凭证
function createVoucher() {
    const businessData = {
        type: selectedBusinessType,
        amount: parseFloat($('#businessAmount').val()),
        description: $('#businessDescription').val(),
        date: $('#businessDate').val()
    };

    $.post('/financial/assistant/auto-generate', businessData)
        .done(function(response) {
            if (response.success) {
                createdVoucherId = response.voucher_id;
                $('#createdVoucherNumber').text(response.voucher_number);
                updateStepDisplay();
            } else {
                showMessage('创建凭证失败：' + response.message, 'error');
            }
        })
        .fail(function() {
            showMessage('创建凭证失败', 'error');
        });
}

// 查看凭证
function viewVoucher() {
    if (createdVoucherId) {
        window.location.href = `/financial/vouchers/${createdVoucherId}`;
    }
}

// 再创建一个
function createAnother() {
    location.reload();
}

// 格式化货币
function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// 显示消息
function showMessage(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'success' ? 'alert-success' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.wizard-content').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
