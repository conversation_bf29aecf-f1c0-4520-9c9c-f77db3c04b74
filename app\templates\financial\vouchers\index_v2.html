{% extends "base.html" %}

{% block title %}财务凭证管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/yonyou-theme.css') }}">
<style>
.voucher-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.voucher-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.voucher-header .stats {
    display: flex;
    gap: 30px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-number {
    font-size: 18px;
    font-weight: 600;
}

.stat-label {
    font-size: 13px;
    opacity: 0.9;
}

.search-panel {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.search-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 13px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
}

.form-control-sm {
    font-size: 13px;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn-search {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.btn-reset {
    background: #95a5a6;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 13px;
}

.voucher-table-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.table-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.table-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary-action {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-secondary-action {
    background: #ecf0f1;
    color: #2c3e50;
    border: 1px solid #bdc3c7;
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.voucher-table th,
.voucher-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.voucher-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
    z-index: 10;
}

.voucher-table tbody tr:hover {
    background: #f8f9fa;
}

.voucher-number {
    font-weight: 600;
    color: #3498db;
    cursor: pointer;
}

.voucher-number:hover {
    text-decoration: underline;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.status-draft {
    background: #fff3cd;
    color: #856404;
}

.status-pending {
    background: #cce5ff;
    color: #004085;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-posted {
    background: #e2e3e5;
    color: #383d41;
}

.amount-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-icon {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.btn-view {
    background: #e3f2fd;
    color: #1976d2;
}

.btn-edit {
    background: #fff3e0;
    color: #f57c00;
}

.btn-delete {
    background: #ffebee;
    color: #d32f2f;
}

.btn-icon:hover {
    transform: scale(1.1);
}

.pagination-container {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.pagination-info {
    font-size: 13px;
    color: #6c757d;
}

.pagination {
    display: flex;
    gap: 5px;
}

.page-btn {
    padding: 6px 10px;
    border: 1px solid #ddd;
    background: white;
    color: #495057;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background: #e9ecef;
}

.page-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.batch-operations {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    display: none;
}

.batch-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.selected-count {
    font-size: 13px;
    color: #6c757d;
}

.batch-actions {
    display: flex;
    gap: 10px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.loading-spinner {
    font-size: 24px;
    color: #3498db;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .search-form {
        grid-template-columns: 1fr;
    }
    
    .voucher-header .stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .table-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: center;
    }
    
    .voucher-table {
        font-size: 12px;
    }
    
    .voucher-table th,
    .voucher-table td {
        padding: 8px 10px;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 10px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面头部 -->
    <div class="voucher-header">
        <h2><i class="fas fa-file-invoice"></i> 财务凭证管理</h2>
        <div class="stats">
            <div class="stat-item">
                <i class="fas fa-file-alt"></i>
                <div>
                    <div class="stat-number">{{ statistics.total_vouchers or 0 }}</div>
                    <div class="stat-label">本月凭证</div>
                </div>
            </div>
            <div class="stat-item">
                <i class="fas fa-dollar-sign"></i>
                <div>
                    <div class="stat-number">{{ "%.2f"|format(statistics.total_amount or 0) }}</div>
                    <div class="stat-label">本月金额</div>
                </div>
            </div>
            <div class="stat-item">
                <i class="fas fa-clock"></i>
                <div>
                    <div class="stat-number">{{ statistics.status_breakdown.pending or 0 }}</div>
                    <div class="stat-label">待审核</div>
                </div>
            </div>
            <div class="stat-item">
                <i class="fas fa-edit"></i>
                <div>
                    <div class="stat-number">{{ statistics.status_breakdown.draft or 0 }}</div>
                    <div class="stat-label">草稿</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索面板 -->
    <div class="search-panel">
        <form class="search-form" id="searchForm">
            <div class="form-group">
                <label>关键词</label>
                <input type="text" class="form-control-sm" name="keyword" 
                       value="{{ search_params.keyword }}" placeholder="凭证号或摘要">
            </div>
            <div class="form-group">
                <label>凭证类型</label>
                <select class="form-control-sm" name="voucher_type">
                    <option value="">全部类型</option>
                    <option value="记账凭证" {{ 'selected' if search_params.voucher_type == '记账凭证' }}>记账凭证</option>
                    <option value="收款凭证" {{ 'selected' if search_params.voucher_type == '收款凭证' }}>收款凭证</option>
                    <option value="付款凭证" {{ 'selected' if search_params.voucher_type == '付款凭证' }}>付款凭证</option>
                    <option value="转账凭证" {{ 'selected' if search_params.voucher_type == '转账凭证' }}>转账凭证</option>
                </select>
            </div>
            <div class="form-group">
                <label>状态</label>
                <select class="form-control-sm" name="status">
                    <option value="">全部状态</option>
                    <option value="草稿" {{ 'selected' if search_params.status == '草稿' }}>草稿</option>
                    <option value="待审核" {{ 'selected' if search_params.status == '待审核' }}>待审核</option>
                    <option value="已审核" {{ 'selected' if search_params.status == '已审核' }}>已审核</option>
                    <option value="已记账" {{ 'selected' if search_params.status == '已记账' }}>已记账</option>
                </select>
            </div>
            <div class="form-group">
                <label>开始日期</label>
                <input type="date" class="form-control-sm" name="start_date" 
                       value="{{ search_params.start_date }}">
            </div>
            <div class="form-group">
                <label>结束日期</label>
                <input type="date" class="form-control-sm" name="end_date" 
                       value="{{ search_params.end_date }}">
            </div>
            <div class="form-group">
                <button type="submit" class="btn-search">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>
            <div class="form-group">
                <button type="button" class="btn-reset" onclick="resetSearch()">
                    <i class="fas fa-undo"></i> 重置
                </button>
            </div>
        </form>
    </div>

    <!-- 批量操作面板 -->
    <div class="batch-operations" id="batchOperations">
        <div class="batch-header">
            <div class="selected-count">
                已选择 <span id="selectedCount">0</span> 个凭证
            </div>
            <div class="batch-actions">
                <button class="btn-action btn-primary-action" onclick="batchReview()">
                    <i class="fas fa-check"></i> 批量审核
                </button>
                <button class="btn-action btn-secondary-action" onclick="batchDelete()">
                    <i class="fas fa-trash"></i> 批量删除
                </button>
                <button class="btn-action btn-secondary-action" onclick="cancelBatch()">
                    <i class="fas fa-times"></i> 取消选择
                </button>
            </div>
        </div>
    </div>

    <!-- 凭证表格 -->
    <div class="voucher-table-container">
        <div class="table-header">
            <div class="table-title">凭证列表</div>
            <div class="table-actions">
                <button class="btn-action btn-primary-action" onclick="createVoucher()">
                    <i class="fas fa-plus"></i> 新建凭证
                </button>
                <button class="btn-action btn-secondary-action" onclick="toggleBatchMode()">
                    <i class="fas fa-check-square"></i> 批量操作
                </button>
                <button class="btn-action btn-secondary-action" onclick="exportVouchers()">
                    <i class="fas fa-download"></i> 导出
                </button>
                <button class="btn-action btn-secondary-action" onclick="refreshTable()">
                    <i class="fas fa-sync"></i> 刷新
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="voucher-table">
                <thead>
                    <tr>
                        <th width="40px">
                            <input type="checkbox" id="selectAll" style="display: none;">
                            <span id="selectAllLabel">#</span>
                        </th>
                        <th width="100px">凭证号</th>
                        <th width="100px">日期</th>
                        <th width="80px">类型</th>
                        <th>摘要</th>
                        <th width="120px">金额</th>
                        <th width="80px">状态</th>
                        <th width="80px">来源</th>
                        <th width="100px">创建时间</th>
                        <th width="120px">操作</th>
                    </tr>
                </thead>
                <tbody id="voucherTableBody">
                    {% for voucher in vouchers %}
                    <tr data-voucher-id="{{ voucher.id }}">
                        <td>
                            <input type="checkbox" class="voucher-checkbox" value="{{ voucher.id }}" style="display: none;">
                            <span class="row-number">{{ loop.index }}</span>
                        </td>
                        <td>
                            <span class="voucher-number" onclick="viewVoucher({{ voucher.id }})">
                                {{ voucher.voucher_number }}
                            </span>
                        </td>
                        <td>{{ voucher.voucher_date }}</td>
                        <td>{{ voucher.voucher_type }}</td>
                        <td>{{ voucher.summary or '-' }}</td>
                        <td class="amount-cell">{{ "%.2f"|format(voucher.total_amount) }}</td>
                        <td>
                            <span class="status-badge status-{{ voucher.status|lower|replace('草稿', 'draft')|replace('待审核', 'pending')|replace('已审核', 'approved')|replace('已记账', 'posted') }}">
                                {{ voucher.status }}
                            </span>
                        </td>
                        <td>{{ voucher.source_type }}</td>
                        <td>{{ voucher.created_at[:10] if voucher.created_at else '-' }}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-icon btn-view" onclick="viewVoucher({{ voucher.id }})" title="查看">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if voucher.status in ['草稿', '待审核'] %}
                                <button class="btn-icon btn-edit" onclick="editVoucher({{ voucher.id }})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% endif %}
                                {% if voucher.status == '草稿' %}
                                <button class="btn-icon btn-delete" onclick="deleteVoucher({{ voucher.id }})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <div class="pagination-info">
                显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
                {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 条，
                共 {{ pagination.total }} 条记录
            </div>
            <div class="pagination" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="loading-spinner">
            <i class="fas fa-spinner"></i>
        </div>
        <p>正在处理...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let batchMode = false;
let selectedVouchers = new Set();

$(document).ready(function() {
    initializePage();
    generatePagination();
});

// 初始化页面
function initializePage() {
    // 搜索表单提交
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        searchVouchers();
    });
    
    // 全选复选框
    $('#selectAll').on('change', function() {
        const checked = $(this).prop('checked');
        $('.voucher-checkbox').prop('checked', checked);
        updateSelectedVouchers();
    });
    
    // 单个复选框
    $(document).on('change', '.voucher-checkbox', function() {
        updateSelectedVouchers();
    });
}

// 搜索凭证
function searchVouchers() {
    const formData = new FormData($('#searchForm')[0]);
    const params = new URLSearchParams(formData);
    
    showLoading(true);
    
    window.location.href = `${window.location.pathname}?${params.toString()}`;
}

// 重置搜索
function resetSearch() {
    $('#searchForm')[0].reset();
    window.location.href = window.location.pathname;
}

// 切换批量模式
function toggleBatchMode() {
    batchMode = !batchMode;
    
    if (batchMode) {
        $('.voucher-checkbox').show();
        $('.row-number').hide();
        $('#selectAll').show();
        $('#selectAllLabel').hide();
        $('#batchOperations').show();
    } else {
        $('.voucher-checkbox').hide();
        $('.row-number').show();
        $('#selectAll').hide();
        $('#selectAllLabel').show();
        $('#batchOperations').hide();
        selectedVouchers.clear();
    }
}

// 更新选中的凭证
function updateSelectedVouchers() {
    selectedVouchers.clear();
    $('.voucher-checkbox:checked').each(function() {
        selectedVouchers.add(parseInt($(this).val()));
    });
    
    $('#selectedCount').text(selectedVouchers.size);
    
    // 更新全选状态
    const totalCheckboxes = $('.voucher-checkbox').length;
    const checkedCheckboxes = $('.voucher-checkbox:checked').length;
    
    $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
    $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
}

// 取消批量选择
function cancelBatch() {
    $('.voucher-checkbox').prop('checked', false);
    updateSelectedVouchers();
}

// 批量审核
function batchReview() {
    if (selectedVouchers.size === 0) {
        showMessage('请选择要审核的凭证', 'warning');
        return;
    }
    
    if (confirm(`确定要审核选中的 ${selectedVouchers.size} 个凭证吗？`)) {
        performBatchOperation('review');
    }
}

// 批量删除
function batchDelete() {
    if (selectedVouchers.size === 0) {
        showMessage('请选择要删除的凭证', 'warning');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedVouchers.size} 个凭证吗？此操作不可恢复！`)) {
        performBatchOperation('delete');
    }
}

// 执行批量操作
function performBatchOperation(operation) {
    showLoading(true);
    
    $.ajax({
        url: '/financial/vouchers-v2/batch-operations',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            operation: operation,
            voucher_ids: Array.from(selectedVouchers)
        }),
        success: function(response) {
            if (response.success) {
                showMessage(`批量${operation === 'review' ? '审核' : '删除'}完成：成功 ${response.data.success_count} 个，失败 ${response.data.error_count} 个`, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage('批量操作失败：' + response.message, 'error');
            }
        },
        error: function() {
            showMessage('批量操作失败', 'error');
        },
        complete: function() {
            showLoading(false);
        }
    });
}

// 创建凭证
function createVoucher() {
    window.location.href = '/financial/vouchers-v2/create';
}

// 查看凭证
function viewVoucher(voucherId) {
    window.location.href = `/financial/vouchers-v2/${voucherId}`;
}

// 编辑凭证
function editVoucher(voucherId) {
    window.location.href = `/financial/vouchers-v2/${voucherId}/edit`;
}

// 删除凭证
function deleteVoucher(voucherId) {
    if (confirm('确定要删除这个凭证吗？此操作不可恢复！')) {
        performBatchOperation('delete');
    }
}

// 导出凭证
function exportVouchers() {
    showMessage('导出功能开发中...', 'info');
}

// 刷新表格
function refreshTable() {
    location.reload();
}

// 生成分页
function generatePagination() {
    const pagination = {{ pagination | tojsonfilter }};
    const totalPages = Math.ceil(pagination.total / pagination.per_page);
    const currentPage = pagination.page;
    
    let html = '';
    
    // 上一页
    html += `<button class="page-btn" ${currentPage <= 1 ? 'disabled' : ''} onclick="goToPage(${currentPage - 1})">
        <i class="fas fa-chevron-left"></i>
    </button>`;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        html += `<button class="page-btn" onclick="goToPage(1)">1</button>`;
        if (startPage > 2) {
            html += `<span class="page-btn" disabled>...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<span class="page-btn" disabled>...</span>`;
        }
        html += `<button class="page-btn" onclick="goToPage(${totalPages})">${totalPages}</button>`;
    }
    
    // 下一页
    html += `<button class="page-btn" ${currentPage >= totalPages ? 'disabled' : ''} onclick="goToPage(${currentPage + 1})">
        <i class="fas fa-chevron-right"></i>
    </button>`;
    
    $('#pagination').html(html);
}

// 跳转页面
function goToPage(page) {
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    window.location.href = url.toString();
}

// 显示加载
function showLoading(show) {
    if (show) {
        $('#loadingOverlay').show();
    } else {
        $('#loadingOverlay').hide();
    }
}

// 显示消息
function showMessage(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'success' ? 'alert-success' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
