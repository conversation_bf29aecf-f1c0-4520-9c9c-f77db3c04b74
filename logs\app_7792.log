2025-06-22 00:15:33,920 INFO: 应用启动 - PID: 7792 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-22 00:29:32,307 INFO: 自动填充菜单数据: 早餐=None, 午餐=None, 晚餐=None [in C:\StudentsCMSSP\app\services\daily_management_service.py:341]
2025-06-22 00:29:32,336 INFO: 自动创建日志成功: 用户=admin, 学校=系统区域, 日期=2025-06-22 [in C:\StudentsCMSSP\app\services\auto_daily_log_service.py:66]
2025-06-22 00:31:57,612 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 70, in detail_ledger
    return render_template('financial/ledgers/detail.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 364, in block 'financial_content'
    {{ "{:,.2f}"|format(record.balance) }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-22 00:32:40,236 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 00:32:40,245 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 00:32:40,264 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 00:32:40,326 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:32:40,360 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:32:40,367 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:32:40,371 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:32:40,378 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 00:37:07,285 INFO: 批量生成明细账请求: user_area=44, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:142]
2025-06-22 00:37:07,285 INFO: 获取有发生额的科目: area_id=44, year=2025, month=6 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:163]
2025-06-22 00:37:07,290 INFO: 找到有发生额的科目数量: 4 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:165]
2025-06-22 00:37:07,291 INFO: 生成明细账: subject_id=206 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:07,330 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 206, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 41402.6, 41402.6, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:07,334 INFO: 生成明细账: subject_id=207 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:07,353 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 207, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 19574.449999999997, 19574.449999999997, 0.0, 5, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:07,355 INFO: 生成明细账: subject_id=208 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:07,371 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 208, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 81771.15, 81771.15, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:07,372 INFO: 生成明细账: subject_id=220 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:07,390 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 220, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, -142748.19999999998, 0.0, 142748.19999999998, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:07,392 INFO: 批量生成明细账完成: {'success': True, 'message': '批量生成完成，成功 4 个科目', 'results': [{'subject_id': 206, 'result': {'success': True, 'message': '成功生成 1201-原材料 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': 41402.6, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 206, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 41402.6, 41402.6, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}, {'subject_id': 207, 'result': {'success': True, 'message': '成功生成 120101-蔬菜类 2025年6月明细账', 'records_count': 5, 'opening_balance': 0.0, 'closing_balance': 19574.449999999997, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 207, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 19574.449999999997, 19574.449999999997, 0.0, 5, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}, {'subject_id': 208, 'result': {'success': True, 'message': '成功生成 120102-肉类 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': 81771.15, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 208, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 81771.15, 81771.15, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}, {'subject_id': 220, 'result': {'success': True, 'message': '成功生成 2001-应付账款 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': -142748.19999999998, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 220, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, -142748.19999999998, 0.0, 142748.19999999998, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 7), None, datetime.datetime(2025, 6, 22, 0, 37, 7), datetime.datetime(2025, 6, 22, 0, 37, 7))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}]} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:196]
2025-06-22 00:37:21,325 INFO: 批量生成明细账请求: user_area=44, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:142]
2025-06-22 00:37:21,325 INFO: 获取有发生额的科目: area_id=44, year=2025, month=6 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:163]
2025-06-22 00:37:21,326 INFO: 找到有发生额的科目数量: 4 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:165]
2025-06-22 00:37:21,327 INFO: 生成明细账: subject_id=206 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:21,333 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 206, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 41402.6, 41402.6, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:21,336 INFO: 生成明细账: subject_id=207 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:21,352 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 207, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 19574.449999999997, 19574.449999999997, 0.0, 5, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:21,354 INFO: 生成明细账: subject_id=208 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:21,363 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 208, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 81771.15, 81771.15, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:21,365 INFO: 生成明细账: subject_id=220 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:176]
2025-06-22 00:37:21,373 ERROR: 保存明细账记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (44, 220, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, -142748.19999999998, 0.0, 142748.19999999998, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:764]
2025-06-22 00:37:21,375 INFO: 批量生成明细账完成: {'success': True, 'message': '批量生成完成，成功 4 个科目', 'results': [{'subject_id': 206, 'result': {'success': True, 'message': '成功生成 1201-原材料 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': 41402.6, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 206, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 41402.6, 41402.6, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}, {'subject_id': 207, 'result': {'success': True, 'message': '成功生成 120101-蔬菜类 2025年6月明细账', 'records_count': 5, 'opening_balance': 0.0, 'closing_balance': 19574.449999999997, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 207, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 19574.449999999997, 19574.449999999997, 0.0, 5, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}, {'subject_id': 208, 'result': {'success': True, 'message': '成功生成 120102-肉类 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': 81771.15, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 208, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, 81771.15, 81771.15, 0.0, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}, {'subject_id': 220, 'result': {'success': True, 'message': '成功生成 2001-应付账款 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': -142748.19999999998, 'save_result': {'saved': False, 'message': "保存失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO detail_ledgers (area_id, subject_id, year, month, ledger_date, opening_balance, closing_balance, period_debit, period_credit, record_count, status, generated_by, generated_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (44, 220, 2025, 6, datetime.datetime(2025, 6, 22, 0, 0), 0.0, -142748.19999999998, 0.0, 142748.19999999998, 8, '已生成', 38, datetime.datetime(2025, 6, 22, 0, 37, 21), None, datetime.datetime(2025, 6, 22, 0, 37, 21), datetime.datetime(2025, 6, 22, 0, 37, 21))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)"}}}]} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:196]
2025-06-22 00:37:32,918 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 70, in detail_ledger
    return render_template('financial/ledgers/detail.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 364, in block 'financial_content'
    {{ "{:,.2f}"|format(record.balance) }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-22 00:37:54,329 WARNING: [安全监控] 2025-06-22 00:37:54 - 可疑请求 | IP: **************:61116 | 路径: /wordpress/wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 00:38:43,900 WARNING: [安全监控] 2025-06-22 00:38:43 - 可疑请求 | IP: **************:22858 | 路径: /wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 00:42:49,207 INFO: Successfully registered SimSun font [in C:\StudentsCMSSP\app\utils\financial_pdf_generator.py:37]
2025-06-22 00:45:22,688 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-22 00:49:08,913 ERROR: 批量导出失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT financial_vouchers.id AS financial_vouchers_id, financial_vouchers.voucher_number AS financial_vouchers_voucher_number, financial_vouchers.voucher_date AS financial_vouchers_voucher_date, financial_vouchers.area_id AS financial_vouchers_area_id, financial_vouchers.voucher_type AS financial_vouchers_voucher_type, financial_vouchers.summary AS financial_vouchers_summary, financial_vouchers.total_amount AS financial_vouchers_total_amount, financial_vouchers.status AS financial_vouchers_status, financial_vouchers.source_type AS financial_vouchers_source_type, financial_vouchers.source_id AS financial_vouchers_source_id, financial_vouchers.attachment_count AS financial_vouchers_attachment_count, financial_vouchers.created_by AS financial_vouchers_created_by, financial_vouchers.reviewed_by AS financial_vouchers_reviewed_by, financial_vouchers.reviewed_at AS financial_vouchers_reviewed_at, financial_vouchers.posted_by AS financial_vouchers_posted_by, financial_vouchers.posted_at AS financial_vouchers_posted_at, financial_vouchers.notes AS financial_vouchers_notes, financial_vouchers.created_at AS financial_vouchers_created_at, financial_vouchers.updated_at AS financial_vouchers_updated_at, users_1.id AS users_1_id, users_1.username AS users_1_username, users_1.password_hash AS users_1_password_hash, users_1.email AS users_1_email, users_1.real_name AS users_1_real_name, users_1.phone AS users_1_phone, users_1.avatar AS users_1_avatar, users_1.last_login AS users_1_last_login, users_1.status AS users_1_status, users_1.area_id AS users_1_area_id, users_1.area_level AS users_1_area_level, users_1.created_at AS users_1_created_at, users_2.id AS users_2_id, users_2.username AS users_2_username, users_2.password_hash AS users_2_password_hash, users_2.email AS users_2_email, users_2.real_name AS users_2_real_name, users_2.phone AS users_2_phone, users_2.avatar AS users_2_avatar, users_2.last_login AS users_2_last_login, users_2.status AS users_2_status, users_2.area_id AS users_2_area_id, users_2.area_level AS users_2_area_level, users_2.created_at AS users_2_created_at, users_3.id AS users_3_id, users_3.username AS users_3_username, users_3.password_hash AS users_3_password_hash, users_3.email AS users_3_email, users_3.real_name AS users_3_real_name, users_3.phone AS users_3_phone, users_3.avatar AS users_3_avatar, users_3.last_login AS users_3_last_login, users_3.status AS users_3_status, users_3.area_id AS users_3_area_id, users_3.area_level AS users_3_area_level, users_3.created_at AS users_3_created_at 
FROM financial_vouchers LEFT OUTER JOIN users AS users_1 ON users_1.id = financial_vouchers.created_by LEFT OUTER JOIN users AS users_2 ON users_2.id = financial_vouchers.reviewed_by LEFT OUTER JOIN users AS users_3 ON users_3.id = financial_vouchers.posted_by 
WHERE financial_vouchers.id IN (?) AND financial_vouchers.area_id = ?]
[parameters: ('54', 44)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:3250]
2025-06-22 00:49:35,238 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 00:49:35,246 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 00:49:35,255 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 00:49:35,308 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:49:35,320 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:49:35,333 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:49:35,340 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:49:35,344 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 00:50:03,269 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 00:50:03,274 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 00:50:03,279 INFO: 匹配条件的食谱有 5 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 00:50:03,292 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:50:03,300 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:50:03,304 INFO:   - 食谱: 炒包菜（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:50:03,309 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:50:03,314 INFO:   - 食谱: 西红柿炒蛋（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 00:50:03,320 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=8, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
