"""
改进后的财务路由示例
展示如何使用新的工具类和服务类来重构财务模块路由
"""

from flask import request, jsonify
from flask_login import login_required
from app.routes.financial import financial_bp
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from app.utils.financial_voucher_service import FinancialVoucherService, PayableService
from app.utils.error_decorators import api_error_handler, validation_required, FinancialValidators
from app.utils.response_helpers import success, error, FinancialResponseHelper


# ===== 改进后的财务凭证路由 =====

@financial_bp.route('/vouchers/create-improved', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
@validation_required(FinancialValidators.validate_voucher_data)
@api_error_handler
def create_voucher_improved(user_area):
    """
    改进后的创建财务凭证接口
    使用新的服务类和错误处理机制
    """
    data = request.get_json()
    
    # 提取凭证基本数据
    voucher_data = {
        'voucher_date': data['voucher_date'],
        'voucher_type': data['voucher_type'],
        'summary': data['summary'],
        'attachment_count': data.get('attachment_count', 0)
    }
    
    # 提取凭证明细数据
    details_data = data.get('details', [])
    
    # 使用服务类创建凭证
    return FinancialVoucherService.create_voucher_with_details(
        voucher_data, details_data, user_area
    )


@financial_bp.route('/vouchers/<int:voucher_id>/review-improved', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
@api_error_handler
def review_voucher_improved(voucher_id, user_area):
    """
    改进后的审核财务凭证接口
    """
    return FinancialVoucherService.review_voucher(voucher_id, user_area)


@financial_bp.route('/vouchers/<int:voucher_id>/validate-balance-improved')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def validate_voucher_balance_improved(voucher_id, user_area):
    """
    改进后的验证凭证借贷平衡接口
    """
    return FinancialVoucherService.validate_voucher_balance(voucher_id)


@financial_bp.route('/vouchers/from-stock-in-improved', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
@api_error_handler
def create_voucher_from_stock_in_improved(user_area):
    """
    改进后的从入库单创建财务凭证接口
    """
    data = request.get_json()
    stock_in_id = data.get('stock_in_id')
    auto_review = data.get('auto_review', True)
    
    if not stock_in_id:
        return error("缺少入库单ID", 400)
    
    return FinancialVoucherService.create_voucher_from_stock_in(
        stock_in_id, user_area, auto_review
    )


# ===== 改进后的应付账款路由 =====

@financial_bp.route('/payables/create-from-stock-in-improved', methods=['POST'])
@login_required
@school_required
@check_permission('应付账款管理', 'create')
@api_error_handler
def create_payable_from_stock_in_improved(user_area):
    """
    改进后的从入库单创建应付账款接口
    """
    data = request.get_json()
    stock_in_id = data.get('stock_in_id')
    
    if not stock_in_id:
        return error("缺少入库单ID", 400)
    
    return PayableService.create_payable_from_stock_in(stock_in_id, user_area)


# ===== 改进后的付款记录路由 =====

@financial_bp.route('/payments/create-improved', methods=['POST'])
@login_required
@school_required
@check_permission('付款记录管理', 'create')
@validation_required(FinancialValidators.validate_payment_data)
@api_error_handler
def create_payment_improved(user_area):
    """
    改进后的创建付款记录接口
    """
    from app.utils.financial_payment_service import PaymentService
    
    data = request.get_json()
    return PaymentService.create_payment_record(data, user_area)


# ===== 改进后的财务报表路由 =====

@financial_bp.route('/reports/trial-balance-improved')
@login_required
@school_required
@check_permission('财务报表', 'view')
@api_error_handler
def trial_balance_improved(user_area):
    """
    改进后的试算平衡表接口
    """
    from app.utils.financial_report_service import FinancialReportService
    
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date or not end_date:
        return error("缺少日期参数", 400)
    
    return FinancialReportService.generate_trial_balance(
        user_area.id, start_date, end_date
    )


@financial_bp.route('/reports/balance-sheet-improved')
@login_required
@school_required
@check_permission('财务报表', 'view')
@api_error_handler
def balance_sheet_improved(user_area):
    """
    改进后的资产负债表接口
    """
    from app.utils.financial_report_service import FinancialReportService
    
    report_date = request.args.get('report_date')
    
    if not report_date:
        return error("缺少报表日期", 400)
    
    return FinancialReportService.generate_balance_sheet(
        user_area.id, report_date
    )


# ===== 改进后的会计科目路由 =====

@financial_bp.route('/subjects/search-improved')
@login_required
@school_required
@check_permission('会计科目管理', 'view')
@api_error_handler
def search_subjects_improved(user_area):
    """
    改进后的会计科目搜索接口
    """
    from app.utils.accounting_subject_service import AccountingSubjectService
    
    keyword = request.args.get('keyword', '')
    subject_type = request.args.get('subject_type')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))
    
    return AccountingSubjectService.search_subjects(
        user_area.id, keyword, subject_type, page, per_page
    )


# ===== 改进后的数据验证示例 =====

def validate_voucher_creation_data(data):
    """
    凭证创建数据验证示例
    """
    required_fields = ['voucher_date', 'voucher_type', 'summary', 'details']
    
    for field in required_fields:
        if not data.get(field):
            return False, f"缺少必填字段: {field}"
    
    # 验证明细数据
    details = data.get('details', [])
    if not details:
        return False, "凭证必须包含明细"
    
    total_debit = 0
    total_credit = 0
    
    for i, detail in enumerate(details):
        if not detail.get('subject_id'):
            return False, f"第{i+1}行缺少会计科目"
        
        debit = float(detail.get('debit_amount', 0))
        credit = float(detail.get('credit_amount', 0))
        
        if debit < 0 or credit < 0:
            return False, f"第{i+1}行金额不能为负数"
        
        if debit > 0 and credit > 0:
            return False, f"第{i+1}行不能同时有借方和贷方金额"
        
        total_debit += debit
        total_credit += credit
    
    # 检查借贷平衡
    if abs(total_debit - total_credit) > 0.01:
        return False, f"借贷不平衡：借方 {total_debit}，贷方 {total_credit}"
    
    return True, ""


# ===== 改进后的错误处理示例 =====

@financial_bp.route('/example/error-handling')
@login_required
@school_required
@api_error_handler
def error_handling_example(user_area):
    """
    错误处理示例
    展示如何使用统一的错误处理机制
    """
    try:
        # 模拟业务逻辑
        data = request.get_json()
        
        if not data:
            raise ValueError("请求数据为空")
        
        # 模拟数据库操作
        from app.utils.db_operations import DatabaseOperationError
        if data.get('simulate_db_error'):
            raise DatabaseOperationError("模拟数据库错误")
        
        # 模拟权限检查
        if data.get('simulate_permission_error'):
            raise PermissionError("模拟权限错误")
        
        return success("操作成功", data)
        
    except ValueError as e:
        # 参数错误会被装饰器自动处理
        raise e
    except Exception as e:
        # 其他错误也会被装饰器自动处理
        raise e


# ===== 改进后的响应格式示例 =====

@financial_bp.route('/example/response-formats')
@login_required
@school_required
def response_formats_example(user_area):
    """
    响应格式示例
    展示如何使用统一的响应格式
    """
    response_type = request.args.get('type', 'success')
    
    if response_type == 'success':
        return success("操作成功", {'id': 123, 'name': '示例数据'})
    
    elif response_type == 'error':
        return error("操作失败", 400, {'field': 'value'})
    
    elif response_type == 'voucher':
        return FinancialResponseHelper.voucher_response(
            123, 'PZ20250621001', "凭证创建成功"
        )
    
    elif response_type == 'balance':
        return FinancialResponseHelper.balance_validation_response(
            True, 1000.0, 1000.0
        )
    
    elif response_type == 'report':
        return FinancialResponseHelper.financial_report_response(
            {'assets': 10000, 'liabilities': 5000},
            '资产负债表',
            '2025-06'
        )
    
    else:
        return error("未知的响应类型", 400)


# ===== 使用说明 =====

"""
改进后的财务模块路由特点：

1. 安全性提升：
   - 使用参数化查询防止SQL注入
   - 统一的数据验证机制
   - 完善的错误处理

2. 代码质量：
   - 消除代码重复
   - 统一的响应格式
   - 清晰的职责分离

3. 维护性：
   - 服务类封装业务逻辑
   - 装饰器处理横切关注点
   - 标准化的开发模式

4. 可测试性：
   - 业务逻辑与路由分离
   - 依赖注入友好
   - 易于编写单元测试

使用方法：
1. 将现有路由逐步迁移到新的模式
2. 使用新的服务类替换直接的数据库操作
3. 应用统一的错误处理装饰器
4. 使用标准化的响应格式

迁移步骤：
1. 创建对应的服务类
2. 重构业务逻辑到服务类
3. 更新路由使用服务类
4. 添加适当的装饰器
5. 统一响应格式
6. 添加单元测试
"""
