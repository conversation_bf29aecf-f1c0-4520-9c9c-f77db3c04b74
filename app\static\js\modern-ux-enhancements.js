/**
 * 现代化用户体验增强脚本
 * 提供丰富的交互效果和用户体验优化
 */

class ModernUX {
    constructor() {
        this.notifications = [];
        this.init();
    }

    init() {
        this.initializeComponents();
        this.bindEvents();
        this.setupAccessibility();
    }

    // ===== 组件初始化 =====
    initializeComponents() {
        this.initTooltips();
        this.initProgressBars();
        this.initLoadingStates();
        this.initFormEnhancements();
    }

    // ===== 通知系统 =====
    showNotification(title, message, type = 'info', duration = 5000) {
        const notification = this.createNotification(title, message, type);
        document.body.appendChild(notification);
        
        // 触发显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notification);
            }, duration);
        }

        this.notifications.push(notification);
        return notification;
    }

    createNotification(title, message, type) {
        const notification = document.createElement('div');
        notification.className = `notification-modern notification-${type}-modern`;
        
        const icons = {
            success: 'fas fa-check',
            warning: 'fas fa-exclamation-triangle',
            danger: 'fas fa-times-circle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <div class="notification-header-modern">
                <div class="notification-icon-modern">
                    <i class="${icons[type] || icons.info}"></i>
                </div>
                <div class="notification-content-modern">
                    <div class="notification-title-modern">${title}</div>
                    <div class="notification-message-modern">${message}</div>
                </div>
                <button class="notification-close-modern" onclick="modernUX.hideNotification(this.closest('.notification-modern'))">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        return notification;
    }

    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    // ===== 加载状态管理 =====
    showLoading(message = '正在处理...', overlay = true) {
        const existingLoader = document.querySelector('.loading-overlay-modern');
        if (existingLoader) {
            this.hideLoading();
        }

        const loader = document.createElement('div');
        loader.className = 'loading-overlay-modern';
        loader.innerHTML = `
            <div class="loading-content-modern">
                <div class="loading-modern"></div>
                <p style="margin-top: 16px; margin-bottom: 0; color: #374151;">${message}</p>
            </div>
        `;

        document.body.appendChild(loader);
        
        // 防止页面滚动
        document.body.style.overflow = 'hidden';
        
        return loader;
    }

    hideLoading() {
        const loader = document.querySelector('.loading-overlay-modern');
        if (loader) {
            loader.remove();
            document.body.style.overflow = '';
        }
    }

    // ===== 进度条管理 =====
    updateProgress(selector, percentage) {
        const progressBar = document.querySelector(`${selector} .progress-bar-modern`);
        if (progressBar) {
            progressBar.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
        }
    }

    animateProgress(selector, targetPercentage, duration = 1000) {
        const progressBar = document.querySelector(`${selector} .progress-bar-modern`);
        if (!progressBar) return;

        const startPercentage = parseFloat(progressBar.style.width) || 0;
        const difference = targetPercentage - startPercentage;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentPercentage = startPercentage + (difference * easeOutCubic);
            
            progressBar.style.width = `${currentPercentage}%`;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    // ===== 表单增强 =====
    initFormEnhancements() {
        // 自动聚焦第一个输入框
        const firstInput = document.querySelector('.form-control-modern:not([disabled]):not([readonly])');
        if (firstInput) {
            firstInput.focus();
        }

        // 表单验证增强
        this.enhanceFormValidation();
        
        // 自动保存功能
        this.initAutoSave();
    }

    enhanceFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('.form-control-modern');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        const type = field.type;
        
        let isValid = true;
        let errorMessage = '';

        if (isRequired && !value) {
            isValid = false;
            errorMessage = '此字段为必填项';
        } else if (value) {
            switch (type) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = '请输入有效的邮箱地址';
                    }
                    break;
                case 'number':
                    if (isNaN(value)) {
                        isValid = false;
                        errorMessage = '请输入有效的数字';
                    }
                    break;
            }
        }

        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    showFieldValidation(field, isValid, errorMessage) {
        const formGroup = field.closest('.form-group-modern');
        if (!formGroup) return;

        // 移除现有的错误状态
        this.clearFieldError(field);

        if (!isValid) {
            field.classList.add('is-invalid');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.style.cssText = 'color: #f56565; font-size: 12px; margin-top: 4px;';
            errorDiv.textContent = errorMessage;
            
            formGroup.appendChild(errorDiv);
        } else {
            field.classList.add('is-valid');
        }
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid', 'is-valid');
        const formGroup = field.closest('.form-group-modern');
        if (formGroup) {
            const errorDiv = formGroup.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
    }

    // ===== 自动保存功能 =====
    initAutoSave() {
        const autoSaveForms = document.querySelectorAll('[data-auto-save]');
        autoSaveForms.forEach(form => {
            let saveTimeout;
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        this.autoSaveForm(form);
                    }, 2000); // 2秒后自动保存
                });
            });
        });
    }

    autoSaveForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // 保存到本地存储
        const formId = form.id || 'auto-save-form';
        localStorage.setItem(`auto-save-${formId}`, JSON.stringify({
            data: data,
            timestamp: Date.now()
        }));

        // 显示保存指示器
        this.showSaveIndicator();
    }

    showSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        indicator.innerHTML = '<i class="fas fa-check"></i> 已自动保存';
        
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.style.opacity = '1';
        }, 100);
        
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 2000);
    }

    // ===== 工具提示 =====
    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => this.showTooltip(e));
            element.addEventListener('mouseleave', () => this.hideTooltip());
        });
    }

    showTooltip(event) {
        const element = event.target;
        const text = element.getAttribute('data-tooltip');
        if (!text) return;

        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-modern';
        tooltip.style.cssText = `
            position: absolute;
            background: #1f2937;
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            white-space: nowrap;
        `;
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        // 定位工具提示
        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`;
        
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 100);
        
        this.currentTooltip = tooltip;
    }

    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.style.opacity = '0';
            setTimeout(() => {
                if (this.currentTooltip && this.currentTooltip.parentNode) {
                    this.currentTooltip.parentNode.removeChild(this.currentTooltip);
                }
                this.currentTooltip = null;
            }, 200);
        }
    }

    // ===== 进度条初始化 =====
    initProgressBars() {
        const progressBars = document.querySelectorAll('.progress-modern');
        progressBars.forEach(bar => {
            const percentage = bar.getAttribute('data-percentage') || 0;
            this.animateProgress(bar, percentage);
        });
    }

    // ===== 加载状态初始化 =====
    initLoadingStates() {
        // 为所有表单提交添加加载状态
        const forms = document.querySelectorAll('form[data-loading]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const loadingMessage = form.getAttribute('data-loading') || '正在提交...';
                this.showLoading(loadingMessage);
            });
        });
    }

    // ===== 事件绑定 =====
    bindEvents() {
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+S 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const activeForm = document.querySelector('form:focus-within');
                if (activeForm) {
                    this.autoSaveForm(activeForm);
                }
            }
            
            // Esc 关闭模态框和通知
            if (e.key === 'Escape') {
                this.hideLoading();
                this.notifications.forEach(notification => {
                    this.hideNotification(notification);
                });
            }
        });

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 页面隐藏时自动保存
                const autoSaveForms = document.querySelectorAll('[data-auto-save]');
                autoSaveForms.forEach(form => this.autoSaveForm(form));
            }
        });
    }

    // ===== 可访问性增强 =====
    setupAccessibility() {
        // 为所有交互元素添加焦点管理
        const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
        interactiveElements.forEach(element => {
            if (!element.hasAttribute('tabindex')) {
                element.setAttribute('tabindex', '0');
            }
        });

        // 添加键盘导航支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    // ===== 实用工具方法 =====
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // ===== 动画工具 =====
    fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.display = 'block';
        
        let start = performance.now();
        
        const animate = (timestamp) => {
            const elapsed = timestamp - start;
            const progress = Math.min(elapsed / duration, 1);
            
            element.style.opacity = progress;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    fadeOut(element, duration = 300) {
        let start = performance.now();
        const initialOpacity = parseFloat(getComputedStyle(element).opacity);
        
        const animate = (timestamp) => {
            const elapsed = timestamp - start;
            const progress = Math.min(elapsed / duration, 1);
            
            element.style.opacity = initialOpacity * (1 - progress);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.style.display = 'none';
            }
        };
        
        requestAnimationFrame(animate);
    }
}

// 全局实例
const modernUX = new ModernUX();

// 导出到全局作用域
window.modernUX = modernUX;
