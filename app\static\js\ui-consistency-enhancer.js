/**
 * UI一致性增强脚本
 * 自动修复和统一整个项目的界面元素
 */

class UIConsistencyEnhancer {
    constructor() {
        this.baseFont = '13px';
        this.baseFontFamily = "'Microsoft YaHei', 'PingFang SC', Arial, sans-serif";
        this.baseLineHeight = '1.4';
        this.init();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.enhance());
        } else {
            this.enhance();
        }
    }

    enhance() {
        console.log('🎨 启动UI一致性增强...');
        
        this.fixFontSizes();
        this.fixColors();
        this.fixSpacing();
        this.fixButtons();
        this.fixTables();
        this.fixForms();
        this.fixCards();
        this.fixNavigation();
        this.fixModals();
        this.fixBadges();
        
        console.log('✅ UI一致性增强完成');
    }

    // 修复字体大小不一致问题
    fixFontSizes() {
        console.log('🔤 修复字体大小...');
        
        // 统一基础字体
        document.body.style.fontSize = this.baseFont;
        document.body.style.fontFamily = this.baseFontFamily;
        document.body.style.lineHeight = this.baseLineHeight;
        
        // 修复所有文本元素
        const textElements = document.querySelectorAll('p, span, div, td, th, li, label, input, select, textarea, button');
        textElements.forEach(el => {
            const computedStyle = window.getComputedStyle(el);
            const currentSize = parseFloat(computedStyle.fontSize);
            
            // 如果字体太小或太大，统一为13px
            if (currentSize < 11 || (currentSize > 15 && currentSize < 18)) {
                el.style.fontSize = this.baseFont;
                el.style.fontFamily = this.baseFontFamily;
                el.style.lineHeight = this.baseLineHeight;
            }
        });
        
        // 修复标题字体
        const headings = {
            'h1, .h1': '18px',
            'h2, .h2': '16px', 
            'h3, .h3': '14px',
            'h4, .h4': '13px',
            'h5, .h5': '12px',
            'h6, .h6': '11px'
        };
        
        Object.entries(headings).forEach(([selector, size]) => {
            document.querySelectorAll(selector).forEach(el => {
                el.style.fontSize = size;
                el.style.fontFamily = this.baseFontFamily;
                el.style.lineHeight = '1.2';
            });
        });
    }

    // 修复颜色对比度问题
    fixColors() {
        console.log('🎨 修复颜色对比度...');
        
        // 修复文字和背景同色系问题
        const problematicElements = document.querySelectorAll('*');
        problematicElements.forEach(el => {
            const style = window.getComputedStyle(el);
            const bgColor = style.backgroundColor;
            const textColor = style.color;
            
            // 检查是否是浅色背景配浅色文字
            if (this.isLightColor(bgColor) && this.isLightColor(textColor)) {
                el.style.color = '#202124';
            }
            
            // 检查是否是深色背景配深色文字
            if (this.isDarkColor(bgColor) && this.isDarkColor(textColor)) {
                el.style.color = '#ffffff';
            }
        });
        
        // 统一链接颜色
        document.querySelectorAll('a').forEach(link => {
            if (!link.classList.contains('btn')) {
                link.style.color = '#165dff';
                link.style.textDecoration = 'none';
            }
        });
    }

    // 修复间距不一致
    fixSpacing() {
        console.log('📏 修复间距...');
        
        // 统一卡片间距
        document.querySelectorAll('.card').forEach(card => {
            card.style.marginBottom = '16px';
        });
        
        // 统一表单组间距
        document.querySelectorAll('.form-group, .mb-3').forEach(group => {
            group.style.marginBottom = '12px';
        });
        
        // 统一按钮间距
        document.querySelectorAll('.btn').forEach(btn => {
            btn.style.marginRight = '8px';
            btn.style.marginBottom = '4px';
        });
    }

    // 修复按钮样式
    fixButtons() {
        console.log('🔘 修复按钮样式...');
        
        document.querySelectorAll('.btn').forEach(btn => {
            btn.style.fontSize = this.baseFont;
            btn.style.fontFamily = this.baseFontFamily;
            btn.style.fontWeight = '500';
            btn.style.padding = '8px 16px';
            btn.style.borderRadius = '4px';
            btn.style.border = '1px solid transparent';
            btn.style.cursor = 'pointer';
            btn.style.transition = 'all 150ms ease';
            btn.style.display = 'inline-flex';
            btn.style.alignItems = 'center';
            btn.style.justifyContent = 'center';
            btn.style.gap = '4px';
            btn.style.lineHeight = this.baseLineHeight;
            
            // 小按钮
            if (btn.classList.contains('btn-sm')) {
                btn.style.fontSize = '12px';
                btn.style.padding = '4px 12px';
            }
            
            // 大按钮
            if (btn.classList.contains('btn-lg')) {
                btn.style.fontSize = '14px';
                btn.style.padding = '12px 24px';
            }
        });
    }

    // 修复表格样式
    fixTables() {
        console.log('📊 修复表格样式...');
        
        document.querySelectorAll('table, .table').forEach(table => {
            table.style.fontSize = this.baseFont;
            table.style.fontFamily = this.baseFontFamily;
            table.style.lineHeight = this.baseLineHeight;
        });
        
        document.querySelectorAll('th, td').forEach(cell => {
            cell.style.fontSize = this.baseFont;
            cell.style.padding = '8px 12px';
            cell.style.verticalAlign = 'middle';
            cell.style.borderColor = '#e8eaed';
        });
        
        document.querySelectorAll('th').forEach(header => {
            header.style.backgroundColor = '#f8f9fa';
            header.style.color = '#5f6368';
            header.style.fontWeight = '600';
            header.style.fontSize = '12px';
            header.style.textTransform = 'uppercase';
            header.style.letterSpacing = '0.5px';
        });
    }

    // 修复表单样式
    fixForms() {
        console.log('📝 修复表单样式...');
        
        document.querySelectorAll('input, select, textarea, .form-control').forEach(input => {
            input.style.fontSize = this.baseFont;
            input.style.fontFamily = this.baseFontFamily;
            input.style.lineHeight = this.baseLineHeight;
            input.style.padding = '8px 12px';
            input.style.border = '1px solid #dadce0';
            input.style.borderRadius = '4px';
            input.style.transition = 'border-color 150ms ease, box-shadow 150ms ease';
        });
        
        document.querySelectorAll('label, .form-label').forEach(label => {
            label.style.fontSize = this.baseFont;
            label.style.fontWeight = '500';
            label.style.color = '#5f6368';
            label.style.marginBottom = '4px';
        });
    }

    // 修复卡片样式
    fixCards() {
        console.log('🃏 修复卡片样式...');
        
        document.querySelectorAll('.card').forEach(card => {
            card.style.backgroundColor = '#ffffff';
            card.style.border = '1px solid #e8eaed';
            card.style.borderRadius = '6px';
            card.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        });
        
        document.querySelectorAll('.card-header').forEach(header => {
            header.style.backgroundColor = '#f8f9fa';
            header.style.borderBottom = '1px solid #e8eaed';
            header.style.padding = '12px 16px';
            header.style.fontSize = this.baseFont;
            header.style.fontWeight = '500';
            header.style.color = '#5f6368';
        });
        
        document.querySelectorAll('.card-body').forEach(body => {
            body.style.padding = '16px';
        });
    }

    // 修复导航样式
    fixNavigation() {
        console.log('🧭 修复导航样式...');
        
        document.querySelectorAll('.nav-link').forEach(link => {
            link.style.fontSize = this.baseFont;
            link.style.fontFamily = this.baseFontFamily;
            link.style.fontWeight = '500';
            link.style.color = '#5f6368';
            link.style.padding = '8px 16px';
            link.style.transition = 'all 150ms ease';
        });
        
        document.querySelectorAll('.breadcrumb').forEach(breadcrumb => {
            breadcrumb.style.fontSize = this.baseFont;
            breadcrumb.style.fontFamily = this.baseFontFamily;
        });
    }

    // 修复模态框样式
    fixModals() {
        console.log('🪟 修复模态框样式...');
        
        document.querySelectorAll('.modal-content').forEach(modal => {
            modal.style.borderRadius = '8px';
            modal.style.border = 'none';
            modal.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
        });
        
        document.querySelectorAll('.modal-header').forEach(header => {
            header.style.backgroundColor = '#f8f9fa';
            header.style.borderBottom = '1px solid #e8eaed';
            header.style.padding = '16px';
        });
        
        document.querySelectorAll('.modal-title').forEach(title => {
            title.style.fontSize = '14px';
            title.style.fontWeight = '600';
            title.style.color = '#202124';
        });
        
        document.querySelectorAll('.modal-body').forEach(body => {
            body.style.padding = '16px';
            body.style.fontSize = this.baseFont;
        });
    }

    // 修复徽章样式
    fixBadges() {
        console.log('🏷️ 修复徽章样式...');
        
        document.querySelectorAll('.badge').forEach(badge => {
            badge.style.fontSize = '11px';
            badge.style.fontWeight = '500';
            badge.style.padding = '2px 8px';
            badge.style.borderRadius = '12px';
        });
    }

    // 工具方法：检查是否是浅色
    isLightColor(color) {
        if (!color || color === 'transparent' || color === 'rgba(0, 0, 0, 0)') return false;
        
        const rgb = this.parseColor(color);
        if (!rgb) return false;
        
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128;
    }

    // 工具方法：检查是否是深色
    isDarkColor(color) {
        return !this.isLightColor(color) && color !== 'transparent' && color !== 'rgba(0, 0, 0, 0)';
    }

    // 工具方法：解析颜色值
    parseColor(color) {
        if (!color) return null;
        
        // RGB格式
        const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1]),
                g: parseInt(rgbMatch[2]),
                b: parseInt(rgbMatch[3])
            };
        }
        
        // RGBA格式
        const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);
        if (rgbaMatch) {
            return {
                r: parseInt(rgbaMatch[1]),
                g: parseInt(rgbaMatch[2]),
                b: parseInt(rgbaMatch[3])
            };
        }
        
        return null;
    }

    // 监听动态内容变化
    observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let shouldEnhance = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldEnhance = true;
                }
            });
            
            if (shouldEnhance) {
                // 延迟执行，避免频繁触发
                setTimeout(() => this.enhance(), 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// 自动启动
document.addEventListener('DOMContentLoaded', () => {
    const enhancer = new UIConsistencyEnhancer();
    enhancer.observeChanges();
});

// 导出到全局
window.UIConsistencyEnhancer = UIConsistencyEnhancer;
