"""
财务凭证智能助手路由
基于新的工具类和服务开发的智能助手功能
"""

from flask import request, jsonify, render_template
from flask_login import login_required
from app.routes.financial import financial_bp
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from app.utils.financial_assistant_service import FinancialAssistantService
from app.utils.error_decorators import api_error_handler
from app.utils.response_helpers import success, error


@financial_bp.route('/assistant')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def assistant_index(user_area):
    """财务助手主页"""
    return render_template('financial/assistant/index.html', user_area=user_area)


@financial_bp.route('/assistant/suggest-template', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def suggest_voucher_template(user_area):
    """
    智能推荐凭证模板
    """
    data = request.get_json()
    business_type = data.get('business_type')
    amount = float(data.get('amount', 0))
    
    if not business_type:
        return error("缺少业务类型参数", 400)
    
    if amount <= 0:
        return error("金额必须大于0", 400)
    
    return FinancialAssistantService.suggest_voucher_template(
        business_type, amount, user_area
    )


@financial_bp.route('/assistant/auto-generate', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
@api_error_handler
def auto_generate_voucher(user_area):
    """
    自动生成财务凭证
    """
    data = request.get_json()
    
    required_fields = ['type', 'amount']
    for field in required_fields:
        if not data.get(field):
            return error(f"缺少必填字段: {field}", 400)
    
    return FinancialAssistantService.auto_generate_voucher_from_business(
        data, user_area
    )


@financial_bp.route('/assistant/batch-generate', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
@api_error_handler
def batch_generate_vouchers(user_area):
    """
    批量生成财务凭证
    """
    data = request.get_json()
    stock_in_ids = data.get('stock_in_ids', [])
    
    if not stock_in_ids:
        return error("请选择要生成凭证的入库单", 400)
    
    if len(stock_in_ids) > 50:
        return error("单次批量操作不能超过50条记录", 400)
    
    return FinancialAssistantService.batch_generate_vouchers_from_stock_ins(
        stock_in_ids, user_area
    )


@financial_bp.route('/assistant/validate', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def validate_voucher_data(user_area):
    """
    验证凭证数据并提供修正建议
    """
    data = request.get_json()
    voucher_data = data.get('voucher_data', {})
    details_data = data.get('details_data', [])
    
    return FinancialAssistantService.validate_and_suggest_corrections(
        voucher_data, details_data
    )


@financial_bp.route('/assistant/frequent-subjects')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def get_frequent_subjects(user_area):
    """
    获取常用会计科目
    """
    limit = int(request.args.get('limit', 10))
    
    return FinancialAssistantService.get_frequent_subjects(user_area, limit)


@financial_bp.route('/assistant/statistics')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def get_voucher_statistics(user_area):
    """
    获取凭证统计信息
    """
    period = request.args.get('period', 'month')
    
    if period not in ['week', 'month', 'quarter', 'year']:
        return error("无效的统计周期", 400)
    
    return FinancialAssistantService.get_voucher_statistics(user_area, period)


@financial_bp.route('/assistant/quick-create')
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def quick_create_voucher(user_area):
    """快速创建凭证页面"""
    return render_template('financial/assistant/quick_create.html', user_area=user_area)


@financial_bp.route('/assistant/batch-operations')
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def batch_operations(user_area):
    """批量操作页面"""
    return render_template('financial/assistant/batch_operations.html', user_area=user_area)


@financial_bp.route('/assistant/dashboard')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def assistant_dashboard(user_area):
    """助手仪表板页面"""
    return render_template('financial/assistant/dashboard.html', user_area=user_area)
