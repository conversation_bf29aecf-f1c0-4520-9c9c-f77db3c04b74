{% extends "base.html" %}

{% block title %}财务智能助手{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/modern-ux-enhancements.css') }}">
<style>
.assistant-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.assistant-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.assistant-card h4 {
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.assistant-card h4 i {
    margin-right: 8px;
    color: #3498db;
}

.assistant-card p {
    color: #7f8c8d;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 15px;
}

.btn-assistant {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-assistant:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
    border-left: 4px solid #3498db;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 5px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.page-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.page-header p {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
}

.loading-spinner i {
    font-size: 24px;
    color: #3498db;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .assistant-card {
        padding: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <h2><i class="fas fa-robot"></i> 财务智能助手</h2>
        <p>让AI帮助您更高效地处理财务工作，简化操作流程，提升工作效率</p>
    </div>

    <!-- 统计信息 -->
    <div class="stats-grid" id="statsGrid">
        <div class="stat-item">
            <span class="stat-number" id="totalVouchers">-</span>
            <div class="stat-label">本月凭证</div>
        </div>
        <div class="stat-item">
            <span class="stat-number" id="totalAmount">-</span>
            <div class="stat-label">本月金额</div>
        </div>
        <div class="stat-item">
            <span class="stat-number" id="pendingCount">-</span>
            <div class="stat-label">待审核</div>
        </div>
        <div class="stat-item">
            <span class="stat-number" id="draftCount">-</span>
            <div class="stat-label">草稿</div>
        </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
        <!-- 智能凭证创建 -->
        <div class="assistant-card">
            <h4><i class="fas fa-magic"></i> 智能凭证创建</h4>
            <p>根据业务类型自动推荐凭证模板，智能填充会计科目和金额，让凭证创建更简单快捷。</p>
            <button class="btn btn-assistant" onclick="openQuickCreate()">
                <i class="fas fa-plus"></i> 快速创建
            </button>
        </div>

        <!-- 批量操作 -->
        <div class="assistant-card">
            <h4><i class="fas fa-layer-group"></i> 批量操作</h4>
            <p>一键批量生成入库单对应的财务凭证，支持批量审核、批量导出等操作，提升处理效率。</p>
            <button class="btn btn-assistant" onclick="openBatchOperations()">
                <i class="fas fa-tasks"></i> 批量处理
            </button>
        </div>

        <!-- 凭证验证 -->
        <div class="assistant-card">
            <h4><i class="fas fa-check-circle"></i> 智能验证</h4>
            <p>自动检查凭证的借贷平衡、科目合理性，提供专业的修正建议，确保凭证质量。</p>
            <button class="btn btn-assistant" onclick="openValidator()">
                <i class="fas fa-shield-alt"></i> 验证凭证
            </button>
        </div>

        <!-- 常用科目 -->
        <div class="assistant-card">
            <h4><i class="fas fa-star"></i> 常用科目</h4>
            <p>基于使用频率智能推荐常用会计科目，快速选择，减少查找时间。</p>
            <button class="btn btn-assistant" onclick="showFrequentSubjects()">
                <i class="fas fa-list"></i> 查看科目
            </button>
        </div>

        <!-- 数据分析 -->
        <div class="assistant-card">
            <h4><i class="fas fa-chart-bar"></i> 数据分析</h4>
            <p>提供财务数据的可视化分析，包括凭证趋势、科目使用情况等统计信息。</p>
            <button class="btn btn-assistant" onclick="openDashboard()">
                <i class="fas fa-analytics"></i> 查看分析
            </button>
        </div>

        <!-- 模板管理 -->
        <div class="assistant-card">
            <h4><i class="fas fa-file-alt"></i> 模板管理</h4>
            <p>管理和自定义凭证模板，保存常用的凭证格式，提高录入效率。</p>
            <button class="btn btn-assistant" onclick="openTemplates()">
                <i class="fas fa-cog"></i> 管理模板
            </button>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="loading-spinner" id="loadingSpinner">
        <i class="fas fa-spinner"></i>
        <p>正在加载数据...</p>
    </div>
</div>

<!-- 常用科目模态框 -->
<div class="modal fade" id="frequentSubjectsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">常用会计科目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>科目编码</th>
                                <th>科目名称</th>
                                <th>科目类型</th>
                                <th>使用次数</th>
                            </tr>
                        </thead>
                        <tbody id="frequentSubjectsTable">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadStatistics();
});

// 加载统计信息
function loadStatistics() {
    showLoading(true);
    
    $.get('/financial/assistant/statistics?period=month')
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#totalVouchers').text(data.total_vouchers);
                $('#totalAmount').text(formatCurrency(data.total_amount));
                $('#pendingCount').text(data.status_breakdown.pending);
                $('#draftCount').text(data.status_breakdown.draft);
            }
        })
        .fail(function() {
            showMessage('加载统计信息失败', 'error');
        })
        .always(function() {
            showLoading(false);
        });
}

// 打开快速创建
function openQuickCreate() {
    window.location.href = '/financial/assistant/quick-create';
}

// 打开批量操作
function openBatchOperations() {
    window.location.href = '/financial/assistant/batch-operations';
}

// 打开验证器
function openValidator() {
    window.location.href = '/financial/vouchers/create?mode=validate';
}

// 显示常用科目
function showFrequentSubjects() {
    $.get('/financial/assistant/frequent-subjects?limit=20')
        .done(function(response) {
            if (response.success) {
                const subjects = response.data;
                let html = '';
                
                subjects.forEach(function(subject) {
                    html += `
                        <tr>
                            <td>${subject.code}</td>
                            <td>${subject.name}</td>
                            <td>${subject.subject_type}</td>
                            <td><span class="badge bg-primary">${subject.usage_count}</span></td>
                        </tr>
                    `;
                });
                
                $('#frequentSubjectsTable').html(html);
                $('#frequentSubjectsModal').modal('show');
            }
        })
        .fail(function() {
            showMessage('加载常用科目失败', 'error');
        });
}

// 打开仪表板
function openDashboard() {
    window.location.href = '/financial/assistant/dashboard';
}

// 打开模板管理
function openTemplates() {
    showMessage('模板管理功能开发中...', 'info');
}

// 显示加载动画
function showLoading(show) {
    if (show) {
        $('#loadingSpinner').show();
        $('#statsGrid').hide();
    } else {
        $('#loadingSpinner').hide();
        $('#statsGrid').show();
    }
}

// 格式化货币
function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2
    }).format(amount);
}

// 显示消息
function showMessage(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'success' ? 'alert-success' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
