"""
重构后的财务凭证管理路由
使用新的工具类和服务，提供更安全、更高效的实现
"""

from flask import render_template, request, redirect, url_for, jsonify, current_app
from flask_login import login_required, current_user
from app.routes.financial import financial_bp
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from app.utils.financial_voucher_service import FinancialVoucherService
from app.utils.financial_assistant_service import FinancialAssistantService
from app.utils.error_decorators import financial_error_handler, api_error_handler, FinancialValidators
from app.utils.response_helpers import success, error, paginated, FinancialResponseHelper
from app.utils.db_operations import FinancialDatabaseOperations, DatabaseOperations
from app.models_financial import FinancialVoucher, VoucherDetail, AccountingSubject
from datetime import datetime, date
from sqlalchemy import func, desc


@financial_bp.route('/vouchers-v2')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def vouchers_index_v2(user_area):
    """
    重构后的财务凭证列表页面
    使用新的查询方式和响应格式
    """
    # 获取查询参数
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))
    keyword = request.args.get('keyword', '').strip()
    voucher_type = request.args.get('voucher_type', '').strip()
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    
    # 构建查询条件
    conditions = {'area_id': user_area.id}
    
    # 构建复杂查询SQL
    where_clauses = ['area_id = :area_id']
    params = {'area_id': user_area.id}
    
    if keyword:
        where_clauses.append('(voucher_number LIKE :keyword OR summary LIKE :keyword)')
        params['keyword'] = f'%{keyword}%'
    
    if voucher_type:
        where_clauses.append('voucher_type = :voucher_type')
        params['voucher_type'] = voucher_type
    
    if status:
        where_clauses.append('status = :status')
        params['status'] = status
    
    if start_date:
        where_clauses.append('voucher_date >= :start_date')
        params['start_date'] = start_date
    
    if end_date:
        where_clauses.append('voucher_date <= :end_date')
        params['end_date'] = end_date
    
    where_clause = ' AND '.join(where_clauses)
    
    # 查询总数
    count_sql = f"""
        SELECT COUNT(*) as total
        FROM financial_vouchers
        WHERE {where_clause}
    """
    
    total_result = DatabaseOperations.execute_select(count_sql, params)
    total = total_result[0][0] if total_result else 0
    
    # 查询数据
    offset = (page - 1) * per_page
    data_sql = f"""
        SELECT 
            id, voucher_number, voucher_date, voucher_type, summary,
            total_amount, status, source_type, attachment_count,
            created_by, created_at, reviewed_by, reviewed_at
        FROM financial_vouchers
        WHERE {where_clause}
        ORDER BY voucher_date DESC, voucher_number DESC
        OFFSET :offset ROWS FETCH NEXT :per_page ROWS ONLY
    """
    
    params.update({'offset': offset, 'per_page': per_page})
    vouchers_data = DatabaseOperations.execute_select(data_sql, params)
    
    # 格式化数据
    vouchers = []
    for row in vouchers_data:
        voucher = {
            'id': row[0],
            'voucher_number': row[1],
            'voucher_date': row[2].strftime('%Y-%m-%d') if row[2] else '',
            'voucher_type': row[3],
            'summary': row[4],
            'total_amount': float(row[5]) if row[5] else 0,
            'status': row[6],
            'source_type': row[7],
            'attachment_count': row[8] or 0,
            'created_by': row[9],
            'created_at': row[10].strftime('%Y-%m-%d %H:%M:%S') if row[10] else '',
            'reviewed_by': row[11],
            'reviewed_at': row[12].strftime('%Y-%m-%d %H:%M:%S') if row[12] else ''
        }
        vouchers.append(voucher)
    
    # 如果是AJAX请求，返回JSON
    if request.headers.get('Content-Type') == 'application/json' or request.args.get('format') == 'json':
        return paginated(vouchers, page, per_page, total, "查询成功")
    
    # 获取统计信息
    stats = FinancialAssistantService.get_voucher_statistics(user_area, 'month')
    
    return render_template('financial/vouchers/index_v2.html',
                         vouchers=vouchers,
                         pagination={'page': page, 'per_page': per_page, 'total': total},
                         search_params={
                             'keyword': keyword,
                             'voucher_type': voucher_type,
                             'status': status,
                             'start_date': start_date,
                             'end_date': end_date
                         },
                         statistics=stats.json['data'] if stats else {})


@financial_bp.route('/vouchers-v2/create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def create_voucher_v2(user_area):
    """
    重构后的创建财务凭证
    """
    if request.method == 'GET':
        # 显示创建页面
        return render_template('financial/vouchers/create_v2.html',
                             today=date.today().strftime('%Y-%m-%d'),
                             user_area=user_area)
    
    elif request.method == 'POST':
        # 处理创建请求
        return _handle_voucher_creation(user_area)


@api_error_handler
def _handle_voucher_creation(user_area):
    """处理凭证创建逻辑"""
    data = request.get_json()
    if not data:
        return error("无效的数据", 400)
    
    # 提取凭证数据
    voucher_data = {
        'voucher_date': data.get('voucher_date'),
        'voucher_type': data.get('voucher_type', '记账凭证'),
        'summary': data.get('summary', ''),
        'attachment_count': int(data.get('attachment_count', 0))
    }
    
    # 提取明细数据
    details_data = data.get('details', [])
    
    # 使用服务创建凭证
    return FinancialVoucherService.create_voucher_with_details(
        voucher_data, details_data, user_area
    )


@financial_bp.route('/vouchers-v2/<int:voucher_id>')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def view_voucher_v2(voucher_id, user_area):
    """查看财务凭证详情"""
    # 查询凭证基本信息
    voucher_sql = """
        SELECT 
            id, voucher_number, voucher_date, voucher_type, summary,
            total_amount, status, source_type, source_id, attachment_count,
            created_by, created_at, reviewed_by, reviewed_at, posted_by, posted_at, notes
        FROM financial_vouchers
        WHERE id = :voucher_id AND area_id = :area_id
    """
    
    voucher_result = DatabaseOperations.execute_select(voucher_sql, {
        'voucher_id': voucher_id,
        'area_id': user_area.id
    })
    
    if not voucher_result:
        return error("凭证不存在", 404)
    
    voucher_row = voucher_result[0]
    voucher = {
        'id': voucher_row[0],
        'voucher_number': voucher_row[1],
        'voucher_date': voucher_row[2].strftime('%Y-%m-%d') if voucher_row[2] else '',
        'voucher_type': voucher_row[3],
        'summary': voucher_row[4],
        'total_amount': float(voucher_row[5]) if voucher_row[5] else 0,
        'status': voucher_row[6],
        'source_type': voucher_row[7],
        'source_id': voucher_row[8],
        'attachment_count': voucher_row[9] or 0,
        'created_by': voucher_row[10],
        'created_at': voucher_row[11].strftime('%Y-%m-%d %H:%M:%S') if voucher_row[11] else '',
        'reviewed_by': voucher_row[12],
        'reviewed_at': voucher_row[13].strftime('%Y-%m-%d %H:%M:%S') if voucher_row[13] else '',
        'posted_by': voucher_row[14],
        'posted_at': voucher_row[15].strftime('%Y-%m-%d %H:%M:%S') if voucher_row[15] else '',
        'notes': voucher_row[16]
    }
    
    # 查询凭证明细
    details_sql = """
        SELECT 
            vd.id, vd.line_number, vd.subject_id, vd.summary,
            vd.debit_amount, vd.credit_amount,
            s.code as subject_code, s.name as subject_name
        FROM voucher_details vd
        INNER JOIN accounting_subjects s ON vd.subject_id = s.id
        WHERE vd.voucher_id = :voucher_id
        ORDER BY vd.line_number
    """
    
    details_result = DatabaseOperations.execute_select(details_sql, {
        'voucher_id': voucher_id
    })
    
    details = []
    for detail_row in details_result:
        detail = {
            'id': detail_row[0],
            'line_number': detail_row[1],
            'subject_id': detail_row[2],
            'summary': detail_row[3],
            'debit_amount': float(detail_row[4]) if detail_row[4] else 0,
            'credit_amount': float(detail_row[5]) if detail_row[5] else 0,
            'subject_code': detail_row[6],
            'subject_name': detail_row[7]
        }
        details.append(detail)
    
    voucher['details'] = details
    
    # 如果是AJAX请求，返回JSON
    if request.headers.get('Content-Type') == 'application/json' or request.args.get('format') == 'json':
        return success("查询成功", voucher)
    
    return render_template('financial/vouchers/view_v2.html', voucher=voucher)


@financial_bp.route('/vouchers-v2/<int:voucher_id>/edit', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def edit_voucher_v2(voucher_id, user_area):
    """编辑财务凭证"""
    if request.method == 'GET':
        # 获取凭证数据用于编辑
        voucher_response = view_voucher_v2(voucher_id, user_area)
        if isinstance(voucher_response, tuple):  # 错误响应
            return voucher_response
        
        # 如果是JSON响应，提取数据
        if hasattr(voucher_response, 'json'):
            voucher = voucher_response.json['data']
        else:
            # 重新查询数据
            request.args = request.args.copy()
            request.args['format'] = 'json'
            voucher_response = view_voucher_v2(voucher_id, user_area)
            voucher = voucher_response.json['data']
        
        return render_template('financial/vouchers/edit_v2.html', 
                             voucher=voucher, user_area=user_area)
    
    elif request.method == 'POST':
        return _handle_voucher_update(voucher_id, user_area)


@api_error_handler
def _handle_voucher_update(voucher_id, user_area):
    """处理凭证更新逻辑"""
    data = request.get_json()
    if not data:
        return error("无效的数据", 400)
    
    # 检查凭证是否存在且可编辑
    voucher_check_sql = """
        SELECT status FROM financial_vouchers
        WHERE id = :voucher_id AND area_id = :area_id
    """
    
    check_result = DatabaseOperations.execute_select(voucher_check_sql, {
        'voucher_id': voucher_id,
        'area_id': user_area.id
    })
    
    if not check_result:
        return error("凭证不存在", 404)
    
    current_status = check_result[0][0]
    if current_status in ['已审核', '已记账']:
        return error("已审核的凭证不允许修改", 400)
    
    # 更新凭证基本信息
    voucher_update_data = {
        'voucher_date': data.get('voucher_date'),
        'voucher_type': data.get('voucher_type'),
        'summary': data.get('summary'),
        'attachment_count': int(data.get('attachment_count', 0)),
        'updated_at': datetime.now().replace(microsecond=0)
    }
    
    DatabaseOperations.execute_update(
        'financial_vouchers',
        voucher_update_data,
        {'id': voucher_id}
    )
    
    # 删除现有明细
    DatabaseOperations.execute_delete(
        'voucher_details',
        {'voucher_id': voucher_id}
    )
    
    # 创建新明细
    details_data = data.get('details', [])
    total_debit = 0
    
    for i, detail in enumerate(details_data, 1):
        if detail.get('subject_id'):
            detail_data = {
                'voucher_id': voucher_id,
                'line_number': i,
                'subject_id': detail['subject_id'],
                'summary': detail.get('summary', ''),
                'debit_amount': float(detail.get('debit_amount', 0)),
                'credit_amount': float(detail.get('credit_amount', 0)),
                'created_at': datetime.now().replace(microsecond=0)
            }
            
            DatabaseOperations.execute_insert('voucher_details', detail_data, return_id=False)
            total_debit += detail_data['debit_amount']
    
    # 更新凭证总金额
    DatabaseOperations.execute_update(
        'financial_vouchers',
        {'total_amount': total_debit},
        {'id': voucher_id}
    )
    
    return success("凭证更新成功", {'voucher_id': voucher_id})


@financial_bp.route('/vouchers-v2/<int:voucher_id>/review', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
@api_error_handler
def review_voucher_v2(voucher_id, user_area):
    """审核财务凭证"""
    return FinancialVoucherService.review_voucher(voucher_id, user_area)


@financial_bp.route('/vouchers-v2/<int:voucher_id>/validate')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
@api_error_handler
def validate_voucher_v2(voucher_id, user_area):
    """验证凭证借贷平衡"""
    return FinancialVoucherService.validate_voucher_balance(voucher_id)


@financial_bp.route('/vouchers-v2/batch-operations', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
@api_error_handler
def batch_operations_v2(user_area):
    """批量操作凭证"""
    data = request.get_json()
    operation = data.get('operation')
    voucher_ids = data.get('voucher_ids', [])
    
    if not operation or not voucher_ids:
        return error("缺少操作类型或凭证ID", 400)
    
    if len(voucher_ids) > 100:
        return error("单次批量操作不能超过100条记录", 400)
    
    results = []
    errors = []
    
    for voucher_id in voucher_ids:
        try:
            if operation == 'review':
                result = FinancialVoucherService.review_voucher(voucher_id, user_area)
                results.append({'voucher_id': voucher_id, 'success': True})
            elif operation == 'delete':
                # 检查凭证状态
                check_sql = """
                    SELECT status FROM financial_vouchers
                    WHERE id = :voucher_id AND area_id = :area_id
                """
                check_result = DatabaseOperations.execute_select(check_sql, {
                    'voucher_id': voucher_id,
                    'area_id': user_area.id
                })
                
                if check_result and check_result[0][0] == '草稿':
                    # 删除明细
                    DatabaseOperations.execute_delete('voucher_details', {'voucher_id': voucher_id})
                    # 删除凭证
                    DatabaseOperations.execute_delete('financial_vouchers', {'id': voucher_id})
                    results.append({'voucher_id': voucher_id, 'success': True})
                else:
                    errors.append({'voucher_id': voucher_id, 'error': '只能删除草稿状态的凭证'})
            else:
                errors.append({'voucher_id': voucher_id, 'error': '不支持的操作类型'})
                
        except Exception as e:
            errors.append({'voucher_id': voucher_id, 'error': str(e)})
    
    return success("批量操作完成", {
        'total': len(voucher_ids),
        'success_count': len(results),
        'error_count': len(errors),
        'results': results,
        'errors': errors
    })
