"""
统一错误处理装饰器
提供一致的错误处理和响应格式
"""

from functools import wraps
from flask import jsonify, flash, redirect, url_for, request, current_app
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from app import db
from app.utils.db_operations import DatabaseOperationError
import traceback
from typing import Callable, Any, Dict, Optional


class ErrorResponse:
    """错误响应格式化类"""
    
    @staticmethod
    def json_error(message: str, code: int = 400, details: Dict[str, Any] = None) -> tuple:
        """JSON格式错误响应"""
        response = {
            'success': False,
            'message': message,
            'error_code': code
        }
        if details:
            response['details'] = details
        return jsonify(response), code
    
    @staticmethod
    def json_success(message: str, data: Any = None) -> dict:
        """JSON格式成功响应"""
        response = {
            'success': True,
            'message': message
        }
        if data is not None:
            response['data'] = data
        return jsonify(response)
    
    @staticmethod
    def flash_error(message: str, category: str = 'danger') -> None:
        """Flash消息错误"""
        flash(message, category)
    
    @staticmethod
    def flash_success(message: str, category: str = 'success') -> None:
        """Flash消息成功"""
        flash(message, category)


def financial_error_handler(return_type: str = 'json', redirect_url: str = None):
    """
    财务模块统一错误处理装饰器
    
    Args:
        return_type: 返回类型 ('json' 或 'redirect')
        redirect_url: 重定向URL（当return_type为'redirect'时使用）
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                return f(*args, **kwargs)
            except DatabaseOperationError as e:
                # 数据库操作错误
                db.session.rollback()
                current_app.logger.error(f"数据库操作错误 - 函数: {f.__name__}, 错误: {e.message}")
                
                if return_type == 'json':
                    return ErrorResponse.json_error(e.message, 400, e.details)
                else:
                    ErrorResponse.flash_error(e.message)
                    return redirect(url_for(redirect_url) if redirect_url else request.referrer or '/')
                    
            except IntegrityError as e:
                # 数据完整性错误
                db.session.rollback()
                error_msg = _parse_integrity_error(str(e))
                current_app.logger.error(f"数据完整性错误 - 函数: {f.__name__}, 错误: {str(e)}")
                
                if return_type == 'json':
                    return ErrorResponse.json_error(error_msg, 409)
                else:
                    ErrorResponse.flash_error(error_msg)
                    return redirect(url_for(redirect_url) if redirect_url else request.referrer or '/')
                    
            except SQLAlchemyError as e:
                # 其他SQLAlchemy错误
                db.session.rollback()
                current_app.logger.error(f"数据库错误 - 函数: {f.__name__}, 错误: {str(e)}")
                
                if return_type == 'json':
                    return ErrorResponse.json_error("数据库操作失败，请重试", 500)
                else:
                    ErrorResponse.flash_error("数据库操作失败，请重试")
                    return redirect(url_for(redirect_url) if redirect_url else request.referrer or '/')
                    
            except ValueError as e:
                # 数据验证错误
                current_app.logger.warning(f"数据验证错误 - 函数: {f.__name__}, 错误: {str(e)}")
                
                if return_type == 'json':
                    return ErrorResponse.json_error(f"数据验证失败: {str(e)}", 400)
                else:
                    ErrorResponse.flash_error(f"数据验证失败: {str(e)}")
                    return redirect(url_for(redirect_url) if redirect_url else request.referrer or '/')
                    
            except PermissionError as e:
                # 权限错误
                current_app.logger.warning(f"权限错误 - 函数: {f.__name__}, 错误: {str(e)}")
                
                if return_type == 'json':
                    return ErrorResponse.json_error("权限不足", 403)
                else:
                    ErrorResponse.flash_error("权限不足")
                    return redirect(url_for('main.index'))
                    
            except Exception as e:
                # 未知错误
                db.session.rollback()
                error_details = traceback.format_exc()
                current_app.logger.error(f"未知错误 - 函数: {f.__name__}, 错误: {str(e)}")
                current_app.logger.error(f"错误详情: {error_details}")
                
                if return_type == 'json':
                    return ErrorResponse.json_error("系统错误，请联系管理员", 500)
                else:
                    ErrorResponse.flash_error("系统错误，请联系管理员")
                    return redirect(url_for(redirect_url) if redirect_url else request.referrer or '/')
                    
        return wrapper
    return decorator


def api_error_handler(f: Callable) -> Callable:
    """API接口专用错误处理装饰器"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except DatabaseOperationError as e:
            db.session.rollback()
            current_app.logger.error(f"API数据库操作错误 - {f.__name__}: {e.message}")
            return ErrorResponse.json_error(e.message, 400, e.details)
        except ValueError as e:
            current_app.logger.warning(f"API参数错误 - {f.__name__}: {str(e)}")
            return ErrorResponse.json_error(f"参数错误: {str(e)}", 400)
        except PermissionError as e:
            current_app.logger.warning(f"API权限错误 - {f.__name__}: {str(e)}")
            return ErrorResponse.json_error("权限不足", 403)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"API未知错误 - {f.__name__}: {str(e)}")
            return ErrorResponse.json_error("系统错误", 500)
    return wrapper


def form_error_handler(redirect_route: str = None):
    """表单提交专用错误处理装饰器"""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                return f(*args, **kwargs)
            except DatabaseOperationError as e:
                db.session.rollback()
                current_app.logger.error(f"表单数据库操作错误 - {f.__name__}: {e.message}")
                ErrorResponse.flash_error(e.message)
                return redirect(url_for(redirect_route) if redirect_route else request.referrer or '/')
            except ValueError as e:
                current_app.logger.warning(f"表单数据验证错误 - {f.__name__}: {str(e)}")
                ErrorResponse.flash_error(f"数据验证失败: {str(e)}")
                return redirect(url_for(redirect_route) if redirect_route else request.referrer or '/')
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"表单处理错误 - {f.__name__}: {str(e)}")
                ErrorResponse.flash_error("操作失败，请重试")
                return redirect(url_for(redirect_route) if redirect_route else request.referrer or '/')
        return wrapper
    return decorator


def validation_required(validation_func: Callable):
    """数据验证装饰器"""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            # 获取请求数据
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form.to_dict()
            
            # 执行验证
            is_valid, error_message = validation_func(data)
            if not is_valid:
                raise ValueError(error_message)
            
            return f(*args, **kwargs)
        return wrapper
    return decorator


def transaction_required(f: Callable) -> Callable:
    """事务处理装饰器"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            result = f(*args, **kwargs)
            db.session.commit()
            return result
        except Exception as e:
            db.session.rollback()
            raise e
    return wrapper


def _parse_integrity_error(error_message: str) -> str:
    """解析数据完整性错误消息"""
    if 'UNIQUE' in error_message or 'unique' in error_message:
        return "记录已存在，违反唯一约束"
    elif 'FOREIGN KEY' in error_message or 'foreign key' in error_message:
        if 'FK_stock_ins_voucher' in error_message:
            return "该凭证已被入库单引用，请先处理相关的入库记录"
        elif 'FK_payment_records_voucher' in error_message:
            return "该凭证已被付款记录引用，请先处理相关的付款记录"
        else:
            return "关联记录不存在或已被引用，请检查相关数据"
    elif 'CHECK' in error_message:
        return "数据不符合约束条件"
    else:
        return "数据完整性错误，请检查输入数据"


class FinancialValidators:
    """财务模块数据验证器"""
    
    @staticmethod
    def validate_voucher_data(data: Dict[str, Any]) -> tuple[bool, str]:
        """验证凭证数据"""
        required_fields = ['voucher_date', 'voucher_type', 'summary']
        
        for field in required_fields:
            if not data.get(field):
                return False, f"缺少必填字段: {field}"
        
        # 验证日期格式
        try:
            from datetime import datetime
            datetime.strptime(data['voucher_date'], '%Y-%m-%d')
        except ValueError:
            return False, "日期格式错误，应为YYYY-MM-DD"
        
        return True, ""
    
    @staticmethod
    def validate_voucher_details(details: list) -> tuple[bool, str]:
        """验证凭证明细数据"""
        if not details:
            return False, "凭证必须包含明细"
        
        total_debit = 0
        total_credit = 0
        
        for i, detail in enumerate(details):
            # 检查必填字段
            if not detail.get('subject_id'):
                return False, f"第{i+1}行缺少会计科目"
            
            if not detail.get('summary'):
                return False, f"第{i+1}行缺少摘要"
            
            # 检查金额
            debit = float(detail.get('debit_amount', 0))
            credit = float(detail.get('credit_amount', 0))
            
            if debit < 0 or credit < 0:
                return False, f"第{i+1}行金额不能为负数"
            
            if debit > 0 and credit > 0:
                return False, f"第{i+1}行不能同时有借方和贷方金额"
            
            if debit == 0 and credit == 0:
                return False, f"第{i+1}行必须有借方或贷方金额"
            
            total_debit += debit
            total_credit += credit
        
        # 检查借贷平衡
        if abs(total_debit - total_credit) > 0.01:
            return False, f"借贷不平衡：借方 {total_debit}，贷方 {total_credit}"
        
        return True, ""
    
    @staticmethod
    def validate_payment_data(data: Dict[str, Any]) -> tuple[bool, str]:
        """验证付款数据"""
        required_fields = ['payable_id', 'amount', 'payment_date', 'payment_method']
        
        for field in required_fields:
            if not data.get(field):
                return False, f"缺少必填字段: {field}"
        
        # 验证金额
        try:
            amount = float(data['amount'])
            if amount <= 0:
                return False, "付款金额必须大于0"
        except ValueError:
            return False, "付款金额格式错误"
        
        return True, ""
