alembic==1.13.1
anyio==4.5.2
async-timeout==5.0.1
babel==2.17.0
beancount==2.3.6
beautifulsoup4==4.13.4
blinker==1.8.2
bottle==0.13.3
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
cheroot==10.0.1
click==8.1.8
colorama==0.4.6
cryptography==43.0.3
dnspython==2.6.1
email_validator==2.2.0
et_xmlfile==2.0.0
exceptiongroup==1.3.0
fava==1.29
Flask==3.0.3
Flask-Admin==1.6.1
flask-babel==4.0.0
Flask-Cors==5.0.0
Flask-Login==0.6.3
Flask-Mail==0.10.0
Flask-Migrate==4.1.0
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.1
google-api-core==2.25.0
google-api-python-client==2.171.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
googleapis-common-protos==1.70.0
greenlet==3.1.1
httplib2==0.22.0
idna==3.10
importlib_metadata==8.5.0
importlib_resources==6.4.5
iniconfig==2.1.0
itsdangerous==2.2.0
jaraco.functools==4.1.0
Jinja2==3.1.3
lxml==5.4.0
Mako==1.3.10
markdown2==2.5.1
MarkupSafe==2.1.5
more-itertools==10.5.0
numpy==1.24.4
openpyxl==3.1.5
packaging==25.0
pandas==2.0.3
pdfminer2==20151206
pending==1.3
pillow==10.4.0
pluggy==1.5.0
ply==3.11
proto-plus==1.26.1
protobuf==5.29.5
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pyodbc==5.1.0
pyparsing==3.1.4
pypng==0.20220715.0
pytest==8.3.5
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
pytz==2025.2
qrcode==7.4.2
redis==6.1.0
regex==2024.11.6
reportlab==4.4.1
requests==2.32.3
rsa==4.9.1
simplejson==3.20.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.30
tomli==2.2.1
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.2.3
watchfiles==0.24.0
Werkzeug==3.0.3
WTForms==3.1.2
zipp==3.20.2

# MCP (Model Context Protocol) 依赖
mcp==1.1.0
httpx==0.28.1
anyio==4.5.2
pydantic==2.10.4
typing-extensions==4.13.2
